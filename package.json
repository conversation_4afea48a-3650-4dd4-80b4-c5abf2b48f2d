{"name": "enrollment-vista-pro", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-tooltip": "^1.0.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "lucide-react": "^0.344.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.26.2", "tailwind-merge": "^2.2.1"}, "devDependencies": {"@types/node": "^20.11.24", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "vite": "^6.3.5"}}

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { UserPlus, Users, GraduationCap, Award, BookOpen, Calendar, Settings, FileText } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

const Faculty = () => {
  const facultyMembers = [
    { id: 1, name: "Dr. <PERSON>", department: "Computer Science", position: "Professor", status: "Active", courses: 3 },
    { id: 2, name: "Prof<PERSON> <PERSON>", department: "Mathematics", position: "Associate Professor", status: "Active", courses: 2 },
    { id: 3, name: "Dr. <PERSON>", department: "Biology", position: "Assistant Professor", status: "Active", courses: 4 },
    { id: 4, name: "Prof. <PERSON>", department: "Physics", position: "Professor", status: "On Leave", courses: 0 },
  ];

  return (
    <div className="space-y-8 max-w-6xl mx-auto">
      {/* <PERSON> Header */}
      <div className="text-left">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Faculty Management</h1>
        <p className="text-gray-600 text-sm">
          Manage faculty members, departments, and course assignments
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="shadow-md border-0 bg-gradient-to-br from-blue-50 to-blue-100">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1">
            <CardTitle className="text-xs font-semibold text-blue-900">Total Faculty</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent className="pb-3">
            <div className="text-2xl font-bold text-blue-900">24</div>
            <p className="text-xs text-blue-700">+2 from last month</p>
          </CardContent>
        </Card>

        <Card className="shadow-md border-0 bg-gradient-to-br from-green-50 to-green-100">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1">
            <CardTitle className="text-xs font-semibold text-green-900">Active Courses</CardTitle>
            <GraduationCap className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent className="pb-3">
            <div className="text-2xl font-bold text-green-900">45</div>
            <p className="text-xs text-green-700">This semester</p>
          </CardContent>
        </Card>

        <Card className="shadow-md border-0 bg-gradient-to-br from-purple-50 to-purple-100">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1">
            <CardTitle className="text-xs font-semibold text-purple-900">Departments</CardTitle>
            <Award className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent className="pb-3">
            <div className="text-2xl font-bold text-purple-900">8</div>
            <p className="text-xs text-purple-700">Academic departments</p>
          </CardContent>
        </Card>
      </div>

        {/* Faculty Section Header */}
        <div className="bg-gray-50 p-6 rounded-lg">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-lg font-bold text-gray-900">Faculty Members</h2>
              <p className="text-gray-600 text-xs mt-1">Manage faculty profiles and assignments</p>
            </div>
            <Button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg px-6 py-3">
              <UserPlus className="w-5 h-5 mr-2" />
              Add Faculty Member
            </Button>
          </div>
        </div>

        {/* Faculty Table */}
        <Card className="shadow-lg border-0">
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="font-semibold text-gray-900 py-4">Name</TableHead>
                  <TableHead className="font-semibold text-gray-900 py-4">Department</TableHead>
                  <TableHead className="font-semibold text-gray-900 py-4">Position</TableHead>
                  <TableHead className="font-semibold text-gray-900 py-4">Courses</TableHead>
                  <TableHead className="font-semibold text-gray-900 py-4">Status</TableHead>
                  <TableHead className="font-semibold text-gray-900 py-4">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {facultyMembers.map((faculty) => (
                  <TableRow key={faculty.id} className="hover:bg-gray-50 transition-colors">
                    <TableCell className="font-semibold text-gray-900 py-4">{faculty.name}</TableCell>
                    <TableCell className="py-4">{faculty.department}</TableCell>
                    <TableCell className="py-4">{faculty.position}</TableCell>
                    <TableCell className="py-4 text-center">
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-medium">
                        {faculty.courses}
                      </span>
                    </TableCell>
                    <TableCell className="py-4">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                        faculty.status === 'Active'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {faculty.status}
                      </span>
                    </TableCell>
                    <TableCell className="py-4">
                      <Button variant="outline" size="sm" className="hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300">
                        Edit
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
  );
};

export default Faculty;

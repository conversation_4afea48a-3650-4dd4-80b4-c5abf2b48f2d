import { useState } from "react";

// Local component implementations
const Card = ({ children, className = "" }) => (
  <div className={`rounded-lg border bg-white shadow-sm ${className}`}>
    {children}
  </div>
);

const CardContent = ({ children, className = "" }) => (
  <div className={`p-6 ${className}`}>
    {children}
  </div>
);

interface InputProps {
  value?: string | number;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  type?: string;
  required?: boolean;
  placeholder?: string;
  className?: string;
  accept?: string;
  readOnly?: boolean;
}

const Input = ({ value = "", onChange, type = "text", required = false, placeholder = "", className = "", accept, readOnly }: InputProps) => (
  <input
    type={type}
    value={value}
    onChange={onChange}
    required={required}
    placeholder={placeholder}
    className={`w-full rounded-lg border border-gray-300 px-4 py-3 text-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors ${className}`}
    accept={accept}
    readOnly={readOnly}
  />
);

interface ButtonProps {
  children: React.ReactNode;
  type?: "button" | "submit" | "reset";
  className?: string;
}

const Button = ({ children, type = "button", className = "" }: ButtonProps) => (
  <button
    type={type}
    className={`rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 px-8 py-4 text-white font-semibold hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-4 focus:ring-blue-300 transition-all duration-200 shadow-lg hover:shadow-xl ${className}`}
  >
    {children}
  </button>
);

interface SelectProps {
  value: string;
  onValueChange: (value: string) => void;
  children: React.ReactNode;
  className?: string;
}

const Select = ({ value, onValueChange, children, className = "" }: SelectProps) => (
  <select
    value={value}
    onChange={(e) => onValueChange(e.target.value)}
    className={`w-full rounded-lg border border-gray-300 px-4 py-3 text-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors bg-white ${className}`}
  >
    {children}
  </select>
);

interface SelectItemProps {
  value: string;
  children: React.ReactNode;
}

const SelectItem = ({ value, children }: SelectItemProps) => (
  <option value={value}>{children}</option>
);

interface BadgeProps {
  children: React.ReactNode;
  className?: string;
}

const Badge = ({ children, className = "" }: BadgeProps) => (
  <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${className}`}>
    {children}
  </span>
);

interface CheckboxProps {
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  id: string;
  className?: string;
}

const Checkbox = ({ checked, onCheckedChange, id, className = "" }: CheckboxProps) => (
  <input
    type="checkbox"
    id={id}
    checked={checked}
    onChange={(e) => onCheckedChange(e.target.checked)}
    className={`h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 ${className}`}
  />
);

const yearLevels = ["Grade 11", "Grade 12", "1st Year", "2nd Year", "3rd Year", "4th Year"];
const programs = ["STEM", "ABM", "HUMSS", "BS Computer Science", "BS Information Technology", "BS Education"];
const genders = ["Male", "Female", "Other"];
const civilStatuses = ["Single", "Married", "Widowed", "Separated", "Divorced"];
const studentTypes = ["New", "Transferee", "Returnee"];
const modesOfLearning = ["Face-to-Face", "Online", "Hybrid"];
const enrollmentStatuses = ["Pending", "Approved", "Waitlisted"];
const educationLevels = ["Junior HS", "Senior HS", "College"];

const initialStudents = [];

const getStatusBadge = (status) => {
  switch (status) {
    case "Approved":
      return <Badge className="bg-green-50 text-green-700">Approved</Badge>;
    case "Pending":
      return <Badge className="bg-yellow-50 text-yellow-700">Pending</Badge>;
    case "Waitlisted":
      return <Badge className="bg-blue-50 text-blue-700">Waitlisted</Badge>;
    default:
      return <Badge className="border border-gray-300">{status}</Badge>;
  }
};

const Index = () => {
  const [students, setStudents] = useState(initialStudents);
  const [form, setForm] = useState({
    // Personal Info
    firstName: "",
    middleName: "",
    lastName: "",
    suffix: "",
    gender: "Male",
    dob: "",
    pob: "",
    nationality: "",
    civilStatus: "Single",
    photo: null,
    // Contact Info
    mobile: "",
    email: "",
    homeAddress: "",
    city: "",
    province: "",
    zip: "",
    // Family/Guardian
    fatherName: "",
    fatherOccupation: "",
    fatherContact: "",
    motherName: "",
    motherOccupation: "",
    motherContact: "",
    guardianName: "",
    guardianRelationship: "",
    guardianContact: "",
    // Education
    lastSchool: "",
    schoolAddress: "",
    yearGraduated: "",
    educationLevel: educationLevels[0],
    // Enrollment Details
    schoolYear: "2024-2025",
    semester: "1st Semester",
    yearLevel: yearLevels[0],
    program: programs[0],
    studentType: studentTypes[0],
    modeOfLearning: modesOfLearning[0],
    enrollmentStatus: enrollmentStatuses[0],
    // Consent
    infoAccurate: false,
    agreePrivacy: false,
    dateSubmitted: new Date().toISOString().slice(0, 10),
  });

  const handleChange = (field, value) => {
    setForm((prev) => ({ ...prev, [field]: value }));
  };

  const handleFileChange = (field, file) => {
    setForm((prev) => ({ ...prev, [field]: file }));
  };

  const handleCheckbox = (field, checked) => {
    setForm((prev) => ({ ...prev, [field]: checked }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!form.firstName.trim() || !form.lastName.trim() || !form.infoAccurate || !form.agreePrivacy) return;
    setStudents((prev) => [
      ...prev,
      {
        ...form,
        id: prev.length + 1,
      },
    ]);
    setForm({
      firstName: "",
      middleName: "",
      lastName: "",
      suffix: "",
      gender: "Male",
      dob: "",
      pob: "",
      nationality: "",
      civilStatus: "Single",
      photo: null,
      mobile: "",
      email: "",
      homeAddress: "",
      city: "",
      province: "",
      zip: "",
      fatherName: "",
      fatherOccupation: "",
      fatherContact: "",
      motherName: "",
      motherOccupation: "",
      motherContact: "",
      guardianName: "",
      guardianRelationship: "",
      guardianContact: "",
      lastSchool: "",
      schoolAddress: "",
      yearGraduated: "",
      educationLevel: educationLevels[0],
      schoolYear: "2024-2025",
      semester: "1st Semester",
      yearLevel: yearLevels[0],
      program: programs[0],
      studentType: studentTypes[0],
      modeOfLearning: modesOfLearning[0],
      enrollmentStatus: enrollmentStatuses[0],
      infoAccurate: false,
      agreePrivacy: false,
      dateSubmitted: new Date().toISOString().slice(0, 10),
    });
  };

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-3">Student Enrollment Portal</h1>
        <p className="text-gray-600 text-lg max-w-2xl mx-auto">
          Please fill out the form below to enroll a student. Required fields are marked with *.
        </p>
      </div>

      {/* Enrollment Categories */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 max-w-6xl mx-auto">
        <Card className="shadow-md border-0 bg-gradient-to-br from-blue-50 to-blue-100">
          <div className="flex flex-row items-center justify-between space-y-0 pb-1 p-4">
            <div className="text-xs font-semibold text-blue-900">New Enrollment</div>
            <div className="h-4 w-4 text-blue-600">👤</div>
          </div>
          <div className="pb-3 px-4">
            <p className="text-xs text-blue-700">Register new students</p>
          </div>
        </Card>

        <Card className="shadow-md border-0 bg-gradient-to-br from-green-50 to-green-100">
          <div className="flex flex-row items-center justify-between space-y-0 pb-1 p-4">
            <div className="text-xs font-semibold text-green-900">Student Records</div>
            <div className="h-4 w-4 text-green-600">📋</div>
          </div>
          <div className="pb-3 px-4">
            <p className="text-xs text-green-700">View enrollment history</p>
          </div>
        </Card>

        <Card className="shadow-md border-0 bg-gradient-to-br from-purple-50 to-purple-100">
          <div className="flex flex-row items-center justify-between space-y-0 pb-1 p-4">
            <div className="text-xs font-semibold text-purple-900">Programs</div>
            <div className="h-4 w-4 text-purple-600">🎓</div>
          </div>
          <div className="pb-3 px-4">
            <p className="text-xs text-purple-700">Available academic programs</p>
          </div>
        </Card>

        <Card className="shadow-md border-0 bg-gradient-to-br from-orange-50 to-orange-100">
          <div className="flex flex-row items-center justify-between space-y-0 pb-1 p-4">
            <div className="text-xs font-semibold text-orange-900">Requirements</div>
            <div className="h-4 w-4 text-orange-600">📄</div>
          </div>
          <div className="pb-3 px-4">
            <p className="text-xs text-orange-700">Enrollment requirements</p>
          </div>
        </Card>
      </div>
      <Card className="shadow-lg border-0 max-w-4xl mx-auto">
        <CardContent className="p-10">
          <form className="space-y-10" onSubmit={handleSubmit}>
              {/* Personal Information */}
              <div className="space-y-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h2 className="text-xl font-semibold text-blue-900 flex items-center gap-2">
                    🧍‍♂️ Personal Information
                  </h2>
                  <p className="text-blue-700 text-sm mt-1">Basic personal details of the student</p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-800">First Name *</label>
                    <Input value={form.firstName} onChange={e => handleChange("firstName", e.target.value)} required />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-800">Middle Name</label>
                    <Input value={form.middleName} onChange={e => handleChange("middleName", e.target.value)} />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-800">Last Name *</label>
                    <Input value={form.lastName} onChange={e => handleChange("lastName", e.target.value)} required />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-800">Suffix</label>
                    <Input value={form.suffix} onChange={e => handleChange("suffix", e.target.value)} placeholder="Jr., Sr., III, etc." />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-800">Gender *</label>
                    <Select value={form.gender} onValueChange={v => handleChange("gender", v)}>
                      {genders.map(g => <SelectItem key={g} value={g}>{g}</SelectItem>)}
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-800">Date of Birth *</label>
                    <Input type="date" value={form.dob} onChange={e => handleChange("dob", e.target.value)} required />
                  </div>
                  <div className="md:col-span-2 space-y-2">
                    <label className="block text-sm font-semibold text-gray-800">Place of Birth</label>
                    <Input value={form.pob} onChange={e => handleChange("pob", e.target.value)} />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-800">Nationality</label>
                    <Input value={form.nationality} onChange={e => handleChange("nationality", e.target.value)} />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-800">Civil Status</label>
                    <Select value={form.civilStatus} onValueChange={v => handleChange("civilStatus", v)}>
                      {civilStatuses.map(s => <SelectItem key={s} value={s}>{s}</SelectItem>)}
                    </Select>
                  </div>
                  <div className="md:col-span-2 space-y-2">
                    <label className="block text-sm font-semibold text-gray-800">Student Photo Upload</label>
                    <Input
                      type="file"
                      accept="image/*"
                      onChange={e => handleFileChange("photo", e.target.files?.[0] || null)}
                      value=""
                      className="file:mr-4 file:py-3 file:px-6 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 file:cursor-pointer"
                    />
                  </div>
                </div>
              </div>
              {/* Contact Information */}
              <div className="space-y-6">
                <div className="bg-green-50 p-4 rounded-lg">
                  <h2 className="text-xl font-semibold text-green-900 flex items-center gap-2">
                    📫 Contact Information
                  </h2>
                  <p className="text-green-700 text-sm mt-1">Contact details and address information</p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Mobile Number</label>
                    <Input value={form.mobile} onChange={e => handleChange("mobile", e.target.value)} />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Email Address</label>
                    <Input type="email" value={form.email} onChange={e => handleChange("email", e.target.value)} />
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium mb-2 text-gray-700">Home Address</label>
                    <Input value={form.homeAddress} onChange={e => handleChange("homeAddress", e.target.value)} />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">City/Municipality</label>
                    <Input value={form.city} onChange={e => handleChange("city", e.target.value)} />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Province/State</label>
                    <Input value={form.province} onChange={e => handleChange("province", e.target.value)} />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">ZIP/Postal Code</label>
                    <Input value={form.zip} onChange={e => handleChange("zip", e.target.value)} />
                  </div>
                </div>
              </div>
              {/* Family/Guardian Details */}
              <div className="space-y-6">
                <div className="bg-purple-50 p-4 rounded-lg">
                  <h2 className="text-xl font-semibold text-purple-900 flex items-center gap-2">
                    👨‍👩‍👧 Family/Guardian Details
                  </h2>
                  <p className="text-purple-700 text-sm mt-1">Parent and guardian information</p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Father's Name</label>
                    <Input value={form.fatherName} onChange={e => handleChange("fatherName", e.target.value)} />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Occupation / Contact Number (Father)</label>
                    <Input value={form.fatherOccupation} onChange={e => handleChange("fatherOccupation", e.target.value)} placeholder="Occupation" />
                    <Input className="mt-1" value={form.fatherContact} onChange={e => handleChange("fatherContact", e.target.value)} placeholder="Contact Number" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Mother's Name</label>
                    <Input value={form.motherName} onChange={e => handleChange("motherName", e.target.value)} />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Occupation / Contact Number (Mother)</label>
                    <Input value={form.motherOccupation} onChange={e => handleChange("motherOccupation", e.target.value)} placeholder="Occupation" />
                    <Input className="mt-1" value={form.motherContact} onChange={e => handleChange("motherContact", e.target.value)} placeholder="Contact Number" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Guardian's Name (if different from parents)</label>
                    <Input value={form.guardianName} onChange={e => handleChange("guardianName", e.target.value)} />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Relationship to Student</label>
                    <Input value={form.guardianRelationship} onChange={e => handleChange("guardianRelationship", e.target.value)} />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Guardian's Contact Number</label>
                    <Input value={form.guardianContact} onChange={e => handleChange("guardianContact", e.target.value)} />
                  </div>
                </div>
              </div>
              {/* Educational Background */}
              <div className="space-y-6">
                <div className="bg-orange-50 p-4 rounded-lg">
                  <h2 className="text-xl font-semibold text-orange-900 flex items-center gap-2">
                    🎓 Educational Background
                  </h2>
                  <p className="text-orange-700 text-sm mt-1">Previous education and academic history</p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Last School Attended</label>
                    <Input value={form.lastSchool} onChange={e => handleChange("lastSchool", e.target.value)} />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">School Address</label>
                    <Input value={form.schoolAddress} onChange={e => handleChange("schoolAddress", e.target.value)} />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Year Graduated</label>
                    <Input value={form.yearGraduated} onChange={e => handleChange("yearGraduated", e.target.value)} />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Education Level Completed</label>
                    <Select value={form.educationLevel} onValueChange={v => handleChange("educationLevel", v)}>
                      {educationLevels.map(lvl => <SelectItem key={lvl} value={lvl}>{lvl}</SelectItem>)}
                    </Select>
                  </div>
                </div>
              </div>
              {/* Enrollment Details */}
              <div className="space-y-6">
                <div className="bg-indigo-50 p-4 rounded-lg">
                  <h2 className="text-xl font-semibold text-indigo-900 flex items-center gap-2">
                    📘 Enrollment Details
                  </h2>
                  <p className="text-indigo-700 text-sm mt-1">Academic program and enrollment preferences</p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">School Year / Semester</label>
                    <Input value={form.schoolYear} onChange={e => handleChange("schoolYear", e.target.value)} placeholder="e.g. 2024-2025" />
                    <Input className="mt-1" value={form.semester} onChange={e => handleChange("semester", e.target.value)} placeholder="e.g. 1st Semester" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Grade Level / Year Level</label>
                    <Select value={form.yearLevel} onValueChange={v => handleChange("yearLevel", v)}>
                      {yearLevels.map(lvl => <SelectItem key={lvl} value={lvl}>{lvl}</SelectItem>)}
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Program / Strand / Course</label>
                    <Select value={form.program} onValueChange={v => handleChange("program", v)}>
                      {programs.map(prog => <SelectItem key={prog} value={prog}>{prog}</SelectItem>)}
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Student Type</label>
                    <Select value={form.studentType} onValueChange={v => handleChange("studentType", v)}>
                      {studentTypes.map(type => <SelectItem key={type} value={type}>{type}</SelectItem>)}
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Mode of Learning</label>
                    <Select value={form.modeOfLearning} onValueChange={v => handleChange("modeOfLearning", v)}>
                      {modesOfLearning.map(mode => <SelectItem key={mode} value={mode}>{mode}</SelectItem>)}
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Enrollment Status</label>
                    <Select value={form.enrollmentStatus} onValueChange={v => handleChange("enrollmentStatus", v)}>
                      {enrollmentStatuses.map(status => <SelectItem key={status} value={status}>{status}</SelectItem>)}
                    </Select>
                  </div>
                </div>
              </div>
              {/* Consent and Declaration */}
              <div className="space-y-6">
                <div className="bg-red-50 p-4 rounded-lg">
                  <h2 className="text-xl font-semibold text-red-900 flex items-center gap-2">
                    ✅ Consent and Declaration
                  </h2>
                  <p className="text-red-700 text-sm mt-1">Required agreements and submission details</p>
                </div>
                <div className="space-y-6">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="infoAccurate" checked={form.infoAccurate} onCheckedChange={v => handleCheckbox("infoAccurate", v)} />
                    <label htmlFor="infoAccurate" className="text-sm">I confirm that all information provided is accurate. *</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="agreePrivacy" checked={form.agreePrivacy} onCheckedChange={v => handleCheckbox("agreePrivacy", v)} />
                    <label htmlFor="agreePrivacy" className="text-sm">I agree to the data privacy policy. *</label>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Date of Submission</label>
                    <Input
                      type="date"
                      value={form.dateSubmitted}
                      readOnly
                      onChange={() => {}}
                      className="bg-gray-50"
                    />
                  </div>
                </div>
              </div>
              <div className="flex justify-center pt-8 border-t-2 border-gray-200">
                <Button type="submit" className="text-lg min-w-[200px]">
                  🎓 Enroll Student
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

      <div className="max-w-4xl mx-auto">
        <h2 className="text-xl font-semibold mb-4">Enrolled Students (Summary)</h2>
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="min-w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="px-4 py-3 text-left font-medium">Name</th>
                    <th className="px-4 py-3 text-left font-medium">Year/Grade</th>
                    <th className="px-4 py-3 text-left font-medium">Program</th>
                    <th className="px-4 py-3 text-left font-medium">Date Submitted</th>
                    <th className="px-4 py-3 text-left font-medium">Enrollment Status</th>
                  </tr>
                </thead>
                <tbody>
                  {students.map((student) => (
                    <tr key={student.id} className="border-b hover:bg-gray-50">
                      <td className="px-4 py-3 font-medium">{student.lastName}, {student.firstName} {student.middleName}</td>
                      <td className="px-4 py-3">{student.yearLevel}</td>
                      <td className="px-4 py-3">{student.program}</td>
                      <td className="px-4 py-3">{student.dateSubmitted}</td>
                      <td className="px-4 py-3">{getStatusBadge(student.enrollmentStatus)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Index;
